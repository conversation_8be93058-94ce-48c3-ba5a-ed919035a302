import os
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)
os.environ["NO_PROXY"] = "*"

import pandas as pd
import requests
import time
import random
import logging
import sys
import io
from PIL import Image
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from openpyxl.drawing.image import Image as XLImage
from openpyxl.utils import get_column_letter

# ========== 配置 ==========
input_file = r'D:\hubtest\获取店铺订单.xlsx'
timestamp = time.strftime("%Y%m%d_%H%M%S")
order_output_file = fr'D:\hubtest\ozon订单信息明细_{timestamp}.xlsx'
log_file = fr'D:\hubtest\ozon订单日志_{timestamp}.log'
use_proxy = False
retries = 3
MAX_WORKERS = 5
# 图片设置
IMAGE_WIDTH = 80  # 图片宽度
IMAGE_HEIGHT = 80  # 图片高度
# ==========================

# ========== 日志配置 ==========
logger = logging.getLogger()
logger.setLevel(logging.INFO)
file_handler = logging.FileHandler(log_file, encoding='utf-8-sig')
file_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(message)s'))
logger.handlers = [file_handler, console_handler]

def log(msg):
    logger.info(msg)

# ========== 状态映射 ==========
status_map = {
    "awaiting_packaging": "待打包",
    "awaiting_deliver": "待发货", 
    "awaiting_approve": "待确认",
    "awaiting_registration": "待入库",
    "acceptance_in_progress": "验货中",
    "arbitration": "纠纷中",
    "client_arbitration": "客户纠纷",
    "delivering": "配送中",
    "driver_pickup": "司机揽件中",
    "not_accepted": "未接单",
    "cancelled": "已取消",
    "sent_by_seller": "已发货",
    "delivered": "已送达",
    "returned": "已退货"
}

# ========== 配送方式映射 ==========
delivery_method_map = {
    "Ozon Логистика": "Ozon物流",
    "Самовывоз": "自提",
    "Курьерская доставка": "快递配送"
}
# ====================================

cutoff_to = datetime.utcnow()
cutoff_from = cutoff_to - timedelta(days=360)
cutoff_to_str = cutoff_to.strftime("%Y-%m-%dT%H:%M:%S.000Z")
cutoff_from_str = cutoff_from.strftime("%Y-%m-%dT%H:%M:%S.000Z")

df = pd.read_excel(input_file)
df = df.dropna(subset=['ID', '授权码'])

all_orders = []
image_cache = {}  # 用于缓存已下载的图片
start_time = time.time()

# 下载并处理图片
def download_image(url, max_width=IMAGE_WIDTH, max_height=IMAGE_HEIGHT):
    if not url:
        return None
    
    # 检查缓存
    if url in image_cache:
        return image_cache[url]
    
    try:
        response = requests.get(
            url, 
            timeout=10,
            proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None
        )
        
        if response.status_code == 200:
            # 使用PIL处理图片
            img = Image.open(io.BytesIO(response.content))
            
            # 调整图片大小，保持比例
            width, height = img.size
            if width > max_width or height > max_height:
                ratio = min(max_width/width, max_height/height)
                new_width = int(width * ratio)
                new_height = int(height * ratio)
                img = img.resize((new_width, new_height), Image.LANCZOS)
            
            # 转换为字节流
            img_byte_arr = io.BytesIO()
            img_format = img.format if img.format else 'JPEG'
            img.save(img_byte_arr, format=img_format)
            img_byte_arr.seek(0)
            
            # 保存到缓存
            image_cache[url] = img_byte_arr
            return img_byte_arr
        else:
            log(f"⚠️ 下载图片失败: {response.status_code}")
            return None
    except Exception as e:
        log(f"⚠️ 处理图片异常: {e}")
        return None

# 获取商品图片信息 - 使用正确的API端点
def get_product_images(client_id, api_key, product_id, offer_id=None, posting_number=None):
    headers = {
        "Client-Id": client_id,
        "Api-Key": api_key,
        "Content-Type": "application/json"
    }
    
    # 方法1: 优先使用 offer_id
    if offer_id:
        try:
            log(f"🔍 尝试通过offer_id获取图片: {offer_id}")
            request_data = {
                "offer_id": [offer_id],
                "product_id": [],
                "sku": []
            }
            
            response = requests.post(
                "https://api-seller.ozon.ru/v3/product/info/list",
                headers=headers,
                json=request_data,
                proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None,
                timeout=10
            )
            
            log(f"📡 offer_id查询响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                items = result.get("items", [])
                
                if items:
                    item = items[0]
                    
                    # 优先使用 primary_image
                    primary_image = item.get("primary_image", "")
                    if primary_image:
                        # 如果是列表，取第一个元素；如果是字符串，直接使用
                        image_url = primary_image[0] if isinstance(primary_image, list) else primary_image
                        log(f"✅ offer_id找到primary_image: {image_url}")
                        return image_url
                    
                    # 其次使用 images 数组的第一张图片
                    images = item.get("images", [])
                    if images and len(images) > 0:
                        image_url = images[0] if isinstance(images[0], str) else images[0]
                        log(f"✅ offer_id找到images: {image_url}")
                        return image_url
                    
                    log(f"⚠️ offer_id查询到商品但无图片")
                else:
                    log(f"⚠️ offer_id未找到商品信息")
            else:
                log(f"❌ offer_id查询失败: {response.text}")
                
        except Exception as e:
            log(f"⚠️ offer_id查询异常: {e}")
    
    # 方法2: 使用 sku (product_id)
    if product_id and str(product_id).isdigit():
        try:
            log(f"🔍 尝试通过sku获取图片: {product_id}")
            request_data = {
                "offer_id": [],
                "product_id": [],
                "sku": [str(product_id)]
            }
            
            response = requests.post(
                "https://api-seller.ozon.ru/v3/product/info/list",
                headers=headers,
                json=request_data,
                proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None,
                timeout=10
            )
            
            log(f"📡 sku查询响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                items = result.get("items", [])
                
                if items:
                    item = items[0]
                    
                    # 优先使用 primary_image
                    primary_image = item.get("primary_image", "")
                    if primary_image:
                        # 如果是列表，取第一个元素；如果是字符串，直接使用
                        image_url = primary_image[0] if isinstance(primary_image, list) else primary_image
                        log(f"✅ sku找到primary_image: {image_url}")
                        return image_url
                    
                    # 其次使用 images 数组的第一张图片
                    images = item.get("images", [])
                    if images and len(images) > 0:
                        image_url = images[0] if isinstance(images[0], str) else images[0]
                        log(f"✅ sku找到images: {image_url}")
                        return image_url
                    
                    log(f"⚠️ sku查询到商品但无图片")
                else:
                    log(f"⚠️ sku未找到商品信息")
            else:
                log(f"❌ sku查询失败: {response.text}")
                
        except Exception as e:
            log(f"⚠️ sku查询异常: {e}")
    
    # 方法3: 使用 product_id
    if product_id and str(product_id).isdigit():
        try:
            log(f"🔍 尝试通过product_id获取图片: {product_id}")
            request_data = {
                "offer_id": [],
                "product_id": [str(product_id)],
                "sku": []
            }
            
            response = requests.post(
                "https://api-seller.ozon.ru/v3/product/info/list",
                headers=headers,
                json=request_data,
                proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None,
                timeout=10
            )
            
            log(f"📡 product_id查询响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                items = result.get("items", [])
                
                if items:
                    item = items[0]
                    
                    # 优先使用 primary_image
                    primary_image = item.get("primary_image", "")
                    if primary_image:
                        # 如果是列表，取第一个元素；如果是字符串，直接使用
                        image_url = primary_image[0] if isinstance(primary_image, list) else primary_image
                        log(f"✅ product_id找到primary_image: {image_url}")
                        return image_url
                    
                    # 其次使用 images 数组的第一张图片
                    images = item.get("images", [])
                    if images and len(images) > 0:
                        image_url = images[0] if isinstance(images[0], str) else images[0]
                        log(f"✅ product_id找到images: {image_url}")
                        return image_url
                    
                    log(f"⚠️ product_id查询到商品但无图片")
                else:
                    log(f"⚠️ product_id未找到商品信息")
            else:
                log(f"❌ product_id查询失败: {response.text}")
                
        except Exception as e:
            log(f"⚠️ product_id查询异常: {e}")
    
    log(f"❌ 所有方法都未能获取到图片")
    return ""

def process_env(row):
    environment_name = row['环境名']
    client_id = str(row['ID']).replace('.0', '')
    api_key = row['授权码']
    headers = {
        "Client-Id": client_id,
        "Api-Key": api_key,
        "Content-Type": "application/json"
    }

    local_orders = []
    offset = 0
    limit = 1000
    has_more = True

    log(f"\n🌍 开始处理环境: {environment_name}")

    while has_more:
        payload = {
            "dir": "ASC",
            "filter": {
                "cutoff_from": cutoff_from_str,
                "cutoff_to": cutoff_to_str
            },
            "limit": limit,
            "offset": offset,
            "with": {
                "analytics_data": True,
                "barcodes": True,
                "financial_data": True,
                "translit": True
            }
        }

        for attempt in range(retries):
            try:
                log(f"📦 请求待处理订单 offset={offset}")
                response = requests.post(
                    "https://api-seller.ozon.ru/v3/posting/fbs/unfulfilled/list",
                    headers=headers,
                    json=payload,
                    proxies={"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"} if use_proxy else None,
                    timeout=20
                )

                log(f"⬅️ Status Code: {response.status_code}")

                if response.status_code == 200:
                    res_json = response.json()
                    result = res_json.get("result", {})
                    postings = result.get("postings", [])

                    if not postings:
                        has_more = False
                        break

                    for posting in postings:
                        status_en = posting.get("status")
                        status_cn = status_map.get(status_en, status_en)
                        
                        delivery_method = posting.get("delivery_method", {})
                        delivery_name = delivery_method.get("name", "")
                        delivery_name_cn = delivery_method_map.get(delivery_name, delivery_name)
                        
                        # 获取客户信息
                        customer = posting.get("customer", {})
                        
                        # 获取地址信息
                        addressee = posting.get("addressee", {})
                        
                        # 获取财务数据
                        financial_data = posting.get("financial_data", {})
                        
                        # 获取分析数据
                        analytics_data = posting.get("analytics_data", {})
                        
                        # 获取货运单号
                        tracking_number = posting.get("tracking_number", "")

                        base = {
                            "环境名": environment_name,
                            "订单编号": posting.get("posting_number"),
                            "货运单号": tracking_number,
                            "order_number": posting.get("order_number"),
                            "order_id": posting.get("order_id"),
                            "status": status_en,
                            "状态中文": status_cn,
                            "created_at": posting.get("created_at"),
                            "in_process_at": posting.get("in_process_at"),
                            "shipment_date": posting.get("shipment_date"),
                            "delivering_date": posting.get("delivering_date"),
                            "delivery_method_id": delivery_method.get("id"),
                            "delivery_method": delivery_name,
                            "delivery_method_cn": delivery_name_cn,
                            "warehouse_id": delivery_method.get("warehouse_id"),
                            "warehouse": delivery_method.get("warehouse"),
                            "tpl_provider_id": delivery_method.get("tpl_provider_id"),
                            "tpl_provider": delivery_method.get("tpl_provider"),
                            "customer_id": customer.get("customer_id"),
                            "customer_name": customer.get("name"),
                            "addressee_name": addressee.get("name"),
                            "addressee_phone": addressee.get("phone"),
                            "city": addressee.get("city"),
                            "region": addressee.get("region"),
                            "country": addressee.get("country"),
                            "zip_code": addressee.get("zip_code"),
                            "address": addressee.get("address"),
                            "comment": addressee.get("comment"),
                            "total_discount_value": financial_data.get("total_discount_value"),
                            "total_discount_percent": financial_data.get("total_discount_percent"),
                            "cluster_from": analytics_data.get("cluster_from"),
                            "cluster_to": analytics_data.get("cluster_to"),
                            "delivery_type": analytics_data.get("delivery_type"),
                            "is_premium": analytics_data.get("is_premium"),
                            "payment_type_group_name": analytics_data.get("payment_type_group_name"),
                            "warehouse_name": analytics_data.get("warehouse_name"),
                            "warehouse_type": analytics_data.get("warehouse_type"),
                            "is_legal": analytics_data.get("is_legal")
                        }

                        for product in posting.get("products", []):
                            data = base.copy()
                            product_id = product.get("sku")
                            offer_id = product.get("offer_id")
                            
                            data["平台SKU"] = product_id
                            data["offer_id"] = offer_id
                            data["name"] = product.get("name")
                            data["商品数量"] = product.get("quantity")
                            data["商品价格"] = product.get("price")
                            data["mandatory_mark"] = product.get("mandatory_mark")
                            data["currency_code"] = product.get("currency_code")
                            
                            # 获取商品图片链接 - 传递更多参数
                            image_url = get_product_images(client_id, api_key, product_id, offer_id, posting.get("posting_number"))
                            data["SKU图片链接"] = image_url
                            
                            # 财务数据
                            financial_data_product = product.get("financial_data", {})
                            data["product_id"] = financial_data_product.get("product_id")
                            data["commission_amount"] = financial_data_product.get("commission_amount")
                            data["commission_percent"] = financial_data_product.get("commission_percent")
                            data["payout"] = financial_data_product.get("payout")
                            data["product_discount_percent"] = financial_data_product.get("product_discount_percent")
                            data["product_discount_value"] = financial_data_product.get("product_discount_value")
                            data["old_price"] = financial_data_product.get("old_price")
                            data["total_discount_value_product"] = financial_data_product.get("total_discount_value")
                            data["total_discount_percent_product"] = financial_data_product.get("total_discount_percent")
                            data["actions"] = str(financial_data_product.get("actions", []))
                            
                            # 条形码信息
                            barcodes = product.get("barcodes", [])
                            data["barcodes"] = ", ".join(barcodes) if barcodes else ""

                            log(f"✅ 订单 {data['订单编号']} 商品 {data['offer_id']} 数量 {data['商品数量']} 价格 {data['商品价格']} 图片 {bool(data['SKU图片链接'])}")
                            local_orders.append(data)

                    offset += limit
                    has_more = result.get("has_next", False)
                    time.sleep(random.uniform(0.3, 1.2))
                    break
                else:
                    log(f"❌ 非200响应: {response.text}")
                    has_more = False
                    break
            except requests.exceptions.RequestException as e:
                log(f"⚠️ 请求异常: {e}，重试 {attempt + 1}/{retries}")
                time.sleep(1)

    return local_orders

with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
    futures = [executor.submit(process_env, row) for _, row in df.iterrows()]
    for future in as_completed(futures):
        result = future.result()
        all_orders.extend(result)

# 创建DataFrame并优化列顺序
df_output = pd.DataFrame(all_orders)

if not df_output.empty:
    # 添加SKU图片列
    if "SKU图片" not in df_output.columns:
        df_output.insert(df_output.columns.get_loc("SKU图片链接"), "SKU图片", "")
    
    # 重新排列列顺序，将你特别需要的字段放在最前面
    column_order = [
        "环境名", "订单编号", "货运单号", "平台SKU", "SKU图片", "SKU图片链接", "商品数量", "商品价格",
        "order_number", "order_id", "status", "状态中文", "created_at", "in_process_at", 
        "shipment_date", "delivering_date", "offer_id", "name", "currency_code",
        "commission_amount", "commission_percent", "payout",
        "product_discount_value", "product_discount_percent",
        "old_price", "total_discount_value_product", "total_discount_percent_product",
        "customer_name", "addressee_name", "addressee_phone", 
        "city", "region", "country", "address", "zip_code",
        "delivery_method", "delivery_method_cn", "warehouse", 
        "payment_type_group_name", "is_premium", "is_legal",
        "barcodes", "mandatory_mark", "actions", "comment"
    ]
    
    # 只保留存在的列
    existing_columns = [col for col in column_order if col in df_output.columns]
    remaining_columns = [col for col in df_output.columns if col not in existing_columns]
    final_columns = existing_columns + remaining_columns
    
    df_output = df_output[final_columns]

# 保存Excel，设置列宽并插入图片
with pd.ExcelWriter(order_output_file, engine='openpyxl') as writer:
    df_output.to_excel(writer, index=False, sheet_name='订单数据')
    worksheet = writer.sheets['订单数据']
    
    # 设置行高以适应图片
    for row_idx in range(2, len(df_output) + 2):  # +2 因为Excel行从1开始，且有标题行
        worksheet.row_dimensions[row_idx].height = 60
    
    # 设置列宽
    for idx, col in enumerate(df_output.columns):
        if idx < 702:  # Excel最多支持702列 (A到ZZZ)
            try:
                # 使用更简单的方法获取列字母
                col_letter = get_column_letter(idx + 1)
                
                # 为图片列设置更宽的宽度
                if col == "SKU图片":
                    worksheet.column_dimensions[col_letter].width = 15
                else:
                    max_len = max(df_output[col].astype(str).map(len).max(), len(str(col))) + 2
                    worksheet.column_dimensions[col_letter].width = min(max_len, 50)
            except Exception as e:
                log(f"⚠️ 设置列宽异常 {idx}: {e}")
                continue
    
    # 获取图片列和URL列的索引
    img_col_idx = df_output.columns.get_loc("SKU图片") + 1  # +1 因为Excel列从1开始
    url_col_idx = df_output.columns.get_loc("SKU图片链接") + 1
    
    # 下载并插入图片
    log("开始下载并插入图片...")
    for row_idx, url in enumerate(df_output["SKU图片链接"], start=2):  # 从第2行开始（跳过标题行）
        if url:
            try:
                img_data = download_image(url)
                if img_data:
                    cell_address = f"{get_column_letter(img_col_idx)}{row_idx}"
                    img = XLImage(img_data)
                    worksheet.add_image(img, cell_address)
                    log(f"✅ 成功插入图片到单元格 {cell_address}")
            except Exception as e:
                log(f"⚠️ 插入图片异常 行{row_idx}: {e}")

end_time = time.time()
elapsed = end_time - start_time
d, rem = divmod(elapsed, 86400)
h, rem = divmod(rem, 3600)
m, s = divmod(rem, 60)

log(f"\n✅ 完成！已保存待处理订单表格：{order_output_file}")
log(f"🌍 总环境数：{len(df)}")
log(f"📦 总订单数：{len(all_orders)}")
log(f"⏱️ 总耗时：{int(d)}天 {int(h)}小时 {int(m)}分钟 {int(s)}秒")
